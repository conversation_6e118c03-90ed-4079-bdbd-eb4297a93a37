#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行转换器的简单脚本
"""

from csv_to_excel_converter import CSVToExcelConverter
import os

def main():
    """主函数"""
    # 输入文件路径
    input_file = "日报客户-工作表1.csv"
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在: {input_file}")
        return
    
    # 创建转换器实例
    converter = CSVToExcelConverter()
    
    # 执行转换
    print("开始转换...")
    result = converter.convert(input_file)
    
    if result:
        print(f"转换成功! 输出文件: {result}")
    else:
        print("转换失败!")

if __name__ == "__main__":
    main()
