# 科陆流水线运维日志转换工具

这是一个专门为科陆流水线运维日志设计的CSV到Excel转换工具，严格按照 `2.md` 中的规范要求开发。

## 功能特点

### 🔄 文件格式转换
- **输入**: CSV格式（逗号分隔值）
- **输出**: XLSX格式（Excel工作簿）
- **输出工作表名称**: 运维日志

### 📊 数据处理功能
- 动态检测输入文件的列数和列名
- 保持所有原始列的数据，除'记录人'列外
- 为每条记录自动添加备注列，内容为"已解决"
- 为每条记录自动添加维护保养情况列
- 按日期分组数据并合并相同日期的单元格

### 📅 月视图功能 (新增)
- **可视化日期选择**: 根据CSV数据生成月历视图，直观显示可用日期
- **灵活日期过滤**: 点击选择需要输出的特定日期，支持多选和跨月选择
- **智能日期识别**: 自动解析多种日期格式（YYYY/MM/DD、YYYY-MM-DD等）
- **批量操作**: 提供全选本月、清除选择等快捷操作
- **选择性过滤**: 可选择启用日期过滤功能，保持向后兼容

### 🎨 格式化功能
- **主标题行**: `科陆流水线日常运维及故障处理情况`
  - 合并所有列
  - 14号宋体加粗字体
  - 居中对齐

- **列标题行**: 标准化的列名
  - 12号宋体加粗白色字体
  - 蓝色背景（#4472C4）
  - 居中对齐
  - 完整边框

- **数据行**: 实际数据内容
  - 11号宋体字体
  - 居中对齐（故障处理情况列左对齐）
  - 自动换行
  - 完整边框

### 📏 布局优化
- **动态列宽设置**:
  - 日期: 15
  - 维护保养情况: 25
  - 故障处理情况: 50
  - 备注: 15
- **统一行高**: 25像素
- **支持文本自动换行**

### 🔤 编码兼容性
支持多种编码格式自动检测：
- utf-8
- gbk
- gb2312
- utf-8-sig
- cp1252

## 安装依赖

```bash
pip install pandas openpyxl -i https://pypi.tuna.tsinghua.edu.cn/simple
```

## 使用方法

### 方法1: 使用图形界面（推荐）

**最简单的使用方式：**
1. 双击 `启动GUI工具.bat` 或运行 `python gui_converter.py`
2. 点击"浏览..."按钮选择CSV文件
3. 选择输出位置（可启用自动命名）
4. 点击"开始转换"

**GUI功能特点：**
- 🖱️ 图形化界面，操作简单直观
- 📁 文件选择对话框，支持浏览选择
- ⚙️ 自动命名功能，按规范生成文件名
- 📊 实时进度显示和转换日志
- 💾 智能路径缓存，自动记住上次使用的目录
- ✅ 转换完成后提供"打开目录"和"打开文件"选项

### 方法2: 使用命令行工具

```bash
# 基本用法（自动生成输出文件名）
python csv_to_excel_converter.py 日报客户-工作表1.csv

# 指定输出文件名
python csv_to_excel_converter.py 日报客户-工作表1.csv -o 自定义文件名.xlsx
```

### 方法3: 使用简单脚本

```bash
python run_converter.py
```

### 方法4: 在Python代码中使用

```python
from csv_to_excel_converter import CSVToExcelConverter

# 创建转换器实例
converter = CSVToExcelConverter()

# 执行转换
result = converter.convert("输入文件.csv")
print(f"转换完成: {result}")
```

## 文件命名规范

输出文件自动命名规则：
- 格式：`科陆流水线运维日志YYYYMMDD.xlsx`
- 使用当前日期作为时间戳
- 保存在输入文件相同目录

例如：`科陆流水线运维日志20250711.xlsx`

## 数据处理逻辑

### 合并逻辑
- 按日期分组数据
- 相同日期的记录合并日期列单元格
- 相同日期的维护保养情况也合并单元格
- 故障处理情况和备注列不合并（每行独立）

### 表格结构
1. **日期**: 相同日期合并单元格
2. **维护保养情况**: 相同日期合并单元格
3. **故障处理情况**: 每行独立，左对齐
4. **备注**: 每行独立，默认"已解决"

## 示例

输入CSV文件：
```
科陆流水线日常运维及故障处理情况,,,,,,,,,,,,,,,,,,,,,,,,,,
日期,故障处理情况,记录人,,,,,,,,,,,,,,,,,,,,,,,,
2025/7/11,三相二号线6号仓压接不到位,尹佳彬,,,,,,,,,,,,,,,,,,,,,,,,
2025/7/11,终端线空箱码垛机卡箱子,申山江,,,,,,,,,,,,,,,,,,,,,,,,
```

输出Excel文件将包含：
- 格式化的标题和表头
- 按日期分组的数据
- 合并的单元格
- 专业的样式设置

## 错误处理

工具包含完善的错误处理机制：
- 多编码格式自动检测
- 空数据检查
- 文件存在性验证
- 详细的错误信息提示

## GUI界面说明

### 界面组件

#### 左侧：文件转换区域
1. **CSV文件选择**: 点击"浏览..."选择要转换的CSV文件
2. **输出位置选择**: 选择Excel文件的保存位置
3. **自动命名选项**: 勾选后自动按规范生成文件名
4. **日期过滤选项**: 勾选"仅输出选中日期的数据"启用日期过滤
5. **开始转换按钮**: 执行转换操作
6. **进度条**: 显示转换进度
7. **状态显示**: 显示当前操作状态
8. **转换日志**: 详细的转换过程信息

#### 右侧：月视图区域
1. **月份导航**: 使用 ◀ ▶ 按钮切换月份
2. **日历网格**: 显示当月日期，蓝色表示可用日期，绿色表示已选中
3. **批量操作**: "全选本月"和"清除选择"按钮
4. **选择状态**: 实时显示已选择的日期数量

### 使用步骤

#### 基本转换流程
1. 启动GUI工具
2. 选择CSV文件（支持中文路径）
3. 系统自动加载日期数据到月视图
4. 选择输出位置或启用自动命名
5. 点击"开始转换"
6. 等待转换完成
7. 选择是否打开文件所在目录

#### 使用月视图进行日期过滤
1. 选择CSV文件后，右侧月视图会显示所有可用日期
2. 在月视图中点击需要的日期（可多选）
3. 勾选"仅输出选中日期的数据"选项
4. 点击"开始转换"
5. 系统会根据选中的日期过滤数据并输出

### 文件列表

#### 主要程序文件
- `gui_converter.py` - GUI版本转换工具（含月视图功能）
- `calendar_view.py` - 月视图组件
- `csv_to_excel_converter.py` - 核心转换逻辑
- `run_converter.py` - 命令行版本

#### 启动文件
- `启动GUI工具.py` - GUI启动脚本
- `启动GUI工具.bat` - Windows批处理启动文件
- `启动月视图演示.bat` - 月视图功能演示

#### 文档和测试
- `README.md` - 使用说明文档
- `月视图功能说明.md` - 月视图功能详细说明
- `演示月视图功能.py` - 月视图功能演示程序
- `测试月视图转换.py` - 月视图转换功能测试

## 技术栈

- **Python 3.7+**
- **pandas**: 数据处理
- **openpyxl**: Excel文件操作
- **tkinter**: GUI界面（Python内置）
- **argparse**: 命令行参数解析
- **threading**: 多线程处理
