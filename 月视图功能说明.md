# 月视图功能使用说明

## 功能概述

新增的月视图功能允许您根据CSV文件中的日期数据生成月历视图，并可以通过点击选择特定日期来过滤输出数据。

## 界面布局

GUI界面现在分为左右两部分：

### 左侧：文件转换区域
- CSV文件选择
- 输出位置选择
- 自动命名选项
- **新增：仅输出选中日期的数据** 复选框
- 转换按钮
- 进度条和状态显示
- 转换日志

### 右侧：月视图区域
- 月份导航按钮（◀ ▶）
- 当前年月显示
- 日历网格（显示可用日期）
- 操作按钮：全选本月、清除选择
- 选择状态显示

## 使用步骤

### 1. 选择CSV文件
1. 点击左侧"浏览..."按钮选择CSV文件
2. 系统会自动解析CSV文件中的日期数据
3. 右侧月视图会显示所有可用的日期（蓝色背景）
4. 不可用的日期显示为灰色

### 2. 选择需要输出的日期
1. 在右侧月视图中点击需要的日期
2. 选中的日期会变为绿色背景
3. 可以选择多个日期
4. 使用"全选本月"按钮选择当前月份的所有可用日期
5. 使用"清除选择"按钮清除所有选择

### 3. 配置输出选项
1. 勾选"仅输出选中日期的数据"复选框启用日期过滤
2. 如果不勾选此选项，将输出所有数据（忽略日期选择）
3. 配置输出文件位置和命名方式

### 4. 执行转换
1. 点击"开始转换"按钮
2. 系统会根据选择的日期过滤数据
3. 转换日志会显示过滤前后的记录数量

## 功能特点

### 智能日期识别
- 支持多种日期格式：YYYY/MM/DD、YYYY-MM-DD、YYYY.MM.DD
- 自动检测CSV文件编码（UTF-8、GBK、GB2312等）
- 自动跳转到第一个可用日期所在的月份

### 灵活的日期选择
- 支持单选和多选日期
- 跨月份选择支持
- 实时显示选择状态
- 快速操作按钮（全选、清除）

### 数据过滤
- 精确的日期匹配
- 保持原有数据结构
- 显示过滤前后的记录数量
- 支持选择性启用过滤功能

## 月视图操作说明

### 日期状态
- **蓝色背景**：可用日期（CSV中存在的日期）
- **绿色背景**：已选中的日期
- **灰色文字**：不可用日期（CSV中不存在的日期）

### 导航操作
- **◀ 按钮**：切换到上一月
- **▶ 按钮**：切换到下一月
- **月份标题**：显示当前年月

### 批量操作
- **全选本月**：选择当前显示月份的所有可用日期
- **清除选择**：清除所有已选择的日期
- **状态显示**：实时显示已选择的日期数量

## 使用示例

### 场景1：输出特定日期的数据
1. 选择CSV文件后，月视图显示7月11日和7月16日有数据
2. 点击7月11日，该日期变为绿色
3. 勾选"仅输出选中日期的数据"
4. 点击"开始转换"
5. 输出的Excel文件只包含7月11日的数据

### 场景2：输出整月数据
1. 选择CSV文件
2. 点击"全选本月"按钮
3. 勾选"仅输出选中日期的数据"
4. 点击"开始转换"
5. 输出当月所有可用日期的数据

### 场景3：输出所有数据（传统模式）
1. 选择CSV文件
2. 不勾选"仅输出选中日期的数据"
3. 点击"开始转换"
4. 输出所有数据，忽略日期选择

## 注意事项

1. **日期格式**：确保CSV文件第一列为日期格式
2. **编码支持**：支持多种中文编码，自动检测
3. **性能**：大文件处理时月视图加载可能需要几秒钟
4. **兼容性**：新功能完全向后兼容，不影响原有转换流程

## 技术实现

- 使用tkinter创建月历界面
- pandas处理CSV数据和日期解析
- 支持多种日期格式的智能识别
- 实时UI状态更新和用户反馈
