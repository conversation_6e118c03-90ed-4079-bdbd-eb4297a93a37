#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
月视图日历组件
用于显示CSV数据中的日期，并支持多选日期进行数据过滤
"""

import tkinter as tk
from tkinter import ttk
import calendar
import datetime
from typing import List, Set, Optional
import pandas as pd


class CalendarView:
    """月视图日历组件"""
    
    def __init__(self, parent, on_date_selection_changed=None):
        """
        初始化月视图
        
        Args:
            parent: 父窗口
            on_date_selection_changed: 日期选择变化回调函数
        """
        self.parent = parent
        self.on_date_selection_changed = on_date_selection_changed
        
        # 当前显示的年月
        self.current_year = datetime.datetime.now().year
        self.current_month = datetime.datetime.now().month
        
        # 可用日期集合（从CSV数据中提取）
        self.available_dates: Set[datetime.date] = set()
        
        # 选中的日期集合
        self.selected_dates: Set[datetime.date] = set()
        
        # 日期按钮字典，用于更新按钮状态
        self.date_buttons = {}
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        self.main_frame = ttk.LabelFrame(self.parent, text="月视图 - 选择日期", padding="10")
        
        # 顶部控制栏
        control_frame = ttk.Frame(self.main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 上一月按钮
        self.prev_button = ttk.Button(control_frame, text="◀", width=3,
                                     command=self.prev_month)
        self.prev_button.pack(side=tk.LEFT)
        
        # 年月显示
        self.month_label = ttk.Label(control_frame, font=("微软雅黑", 12, "bold"))
        self.month_label.pack(side=tk.LEFT, expand=True)
        
        # 下一月按钮
        self.next_button = ttk.Button(control_frame, text="▶", width=3,
                                     command=self.next_month)
        self.next_button.pack(side=tk.RIGHT)
        
        # 日历网格框架
        self.calendar_frame = ttk.Frame(self.main_frame)
        self.calendar_frame.pack(fill=tk.BOTH, expand=True)
        
        # 操作按钮框架
        button_frame = ttk.Frame(self.main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 全选按钮
        self.select_all_button = ttk.Button(button_frame, text="全选本月",
                                           command=self.select_all_month)
        self.select_all_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 清除选择按钮
        self.clear_button = ttk.Button(button_frame, text="清除选择",
                                      command=self.clear_selection)
        self.clear_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 选择状态标签
        self.status_label = ttk.Label(button_frame, text="已选择: 0 天")
        self.status_label.pack(side=tk.RIGHT)
        
        self.update_calendar()
    
    def load_dates_from_csv(self, csv_file_path: str):
        """
        从CSV文件加载可用日期
        
        Args:
            csv_file_path: CSV文件路径
        """
        try:
            # 使用与主程序相同的编码检测逻辑
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'cp1252']
            df = None
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(csv_file_path, encoding=encoding, skiprows=1)
                    break
                except UnicodeDecodeError:
                    continue
                except Exception:
                    continue
            
            if df is None:
                raise Exception("无法读取CSV文件")
            
            # 提取日期列（第一列）
            if len(df.columns) > 0:
                date_column = df.iloc[:, 0]  # 第一列
                
                # 解析日期
                dates = set()
                for date_str in date_column.dropna():
                    try:
                        # 尝试多种日期格式
                        date_formats = ['%Y/%m/%d', '%Y-%m-%d', '%Y.%m.%d', '%m/%d/%Y']
                        parsed_date = None
                        
                        for fmt in date_formats:
                            try:
                                parsed_date = datetime.datetime.strptime(str(date_str).strip(), fmt).date()
                                break
                            except ValueError:
                                continue
                        
                        if parsed_date:
                            dates.add(parsed_date)
                    except Exception:
                        continue
                
                self.available_dates = dates
                
                # 如果有可用日期，跳转到第一个日期所在的月份
                if dates:
                    first_date = min(dates)
                    self.current_year = first_date.year
                    self.current_month = first_date.month
                
                self.update_calendar()
                return True
        
        except Exception as e:
            print(f"加载CSV日期失败: {e}")
            return False
    
    def prev_month(self):
        """切换到上一月"""
        if self.current_month == 1:
            self.current_month = 12
            self.current_year -= 1
        else:
            self.current_month -= 1
        self.update_calendar()
    
    def next_month(self):
        """切换到下一月"""
        if self.current_month == 12:
            self.current_month = 1
            self.current_year += 1
        else:
            self.current_month += 1
        self.update_calendar()
    
    def update_calendar(self):
        """更新日历显示"""
        # 清除现有的日历
        for widget in self.calendar_frame.winfo_children():
            widget.destroy()
        
        self.date_buttons.clear()
        
        # 更新年月标签
        month_name = calendar.month_name[self.current_month]
        self.month_label.config(text=f"{self.current_year}年 {self.current_month}月")
        
        # 创建星期标题
        weekdays = ['一', '二', '三', '四', '五', '六', '日']
        for i, day in enumerate(weekdays):
            label = ttk.Label(self.calendar_frame, text=day, font=("微软雅黑", 9, "bold"))
            label.grid(row=0, column=i, padx=1, pady=1, sticky="nsew")
        
        # 获取当月日历
        cal = calendar.monthcalendar(self.current_year, self.current_month)
        
        # 配置网格权重
        for i in range(7):
            self.calendar_frame.columnconfigure(i, weight=1)
        
        # 创建日期按钮
        for week_num, week in enumerate(cal, 1):
            self.calendar_frame.rowconfigure(week_num, weight=1)
            for day_num, day in enumerate(week):
                if day == 0:
                    # 空白日期
                    label = ttk.Label(self.calendar_frame, text="")
                    label.grid(row=week_num, column=day_num, padx=1, pady=1, sticky="nsew")
                else:
                    # 创建日期对象
                    date_obj = datetime.date(self.current_year, self.current_month, day)
                    
                    # 判断是否为可用日期
                    is_available = date_obj in self.available_dates
                    is_selected = date_obj in self.selected_dates
                    
                    # 创建按钮
                    if is_available:
                        button = tk.Button(self.calendar_frame, text=str(day),
                                         command=lambda d=date_obj: self.toggle_date(d),
                                         font=("微软雅黑", 9),
                                         relief=tk.RAISED,
                                         bd=1)
                        
                        # 设置按钮颜色
                        if is_selected:
                            button.config(bg="#4CAF50", fg="white", relief=tk.SUNKEN)
                        else:
                            button.config(bg="#E3F2FD", fg="#1976D2")
                    else:
                        # 不可用日期显示为灰色
                        button = tk.Label(self.calendar_frame, text=str(day),
                                        font=("微软雅黑", 9),
                                        fg="#CCCCCC", bg="#F5F5F5")
                    
                    button.grid(row=week_num, column=day_num, padx=1, pady=1, sticky="nsew")
                    
                    if is_available:
                        self.date_buttons[date_obj] = button
        
        self.update_status()
    
    def toggle_date(self, date_obj: datetime.date):
        """切换日期选择状态"""
        if date_obj in self.selected_dates:
            self.selected_dates.remove(date_obj)
        else:
            self.selected_dates.add(date_obj)
        
        # 更新按钮状态
        if date_obj in self.date_buttons:
            button = self.date_buttons[date_obj]
            if date_obj in self.selected_dates:
                button.config(bg="#4CAF50", fg="white", relief=tk.SUNKEN)
            else:
                button.config(bg="#E3F2FD", fg="#1976D2", relief=tk.RAISED)
        
        self.update_status()
        
        # 触发回调
        if self.on_date_selection_changed:
            self.on_date_selection_changed(self.selected_dates)
    
    def select_all_month(self):
        """选择当月所有可用日期"""
        current_month_dates = {date for date in self.available_dates 
                              if date.year == self.current_year and date.month == self.current_month}
        
        self.selected_dates.update(current_month_dates)
        self.update_calendar()
        
        # 触发回调
        if self.on_date_selection_changed:
            self.on_date_selection_changed(self.selected_dates)
    
    def clear_selection(self):
        """清除所有选择"""
        self.selected_dates.clear()
        self.update_calendar()
        
        # 触发回调
        if self.on_date_selection_changed:
            self.on_date_selection_changed(self.selected_dates)
    
    def update_status(self):
        """更新选择状态显示"""
        count = len(self.selected_dates)
        self.status_label.config(text=f"已选择: {count} 天")
    
    def get_selected_dates(self) -> List[datetime.date]:
        """获取选中的日期列表"""
        return sorted(list(self.selected_dates))
    
    def pack(self, **kwargs):
        """打包显示组件"""
        self.main_frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """网格显示组件"""
        self.main_frame.grid(**kwargs)
