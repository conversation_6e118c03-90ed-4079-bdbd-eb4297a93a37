#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动GUI转换工具的简单脚本
"""

import sys
import os

def main():
    """主函数"""
    try:
        # 检查依赖包
        import pandas
        import openpyxl
        import tkinter
        
        # 启动GUI应用
        from gui_converter import main as gui_main
        gui_main()
        
    except ImportError as e:
        print(f"缺少依赖包: {e}")
        print("请运行以下命令安装依赖包:")
        print("pip install pandas openpyxl -i https://pypi.tuna.tsinghua.edu.cn/simple")
        input("按回车键退出...")
        sys.exit(1)
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    main()
