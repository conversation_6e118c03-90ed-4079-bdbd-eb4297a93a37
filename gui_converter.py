#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
科陆流水线运维日志CSV转Excel工具 - GUI版本
带有图形用户界面的转换工具
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import datetime
import threading
import json
from csv_to_excel_converter import CSVToExcelConverter
from calendar_view import CalendarView


class ConverterGUI:
    """转换器GUI界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("科陆流水线运维日志转换工具")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)

        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        self.converter = CSVToExcelConverter()
        self.input_file = tk.StringVar()
        self.output_file = tk.StringVar()

        # 路径缓存文件
        self.cache_file = "path_cache.json"
        self.load_cached_paths()

        # 月视图组件
        self.calendar_view = None
        self.selected_dates = set()

        self.setup_ui()

    def load_cached_paths(self):
        """加载缓存的路径"""
        try:
            # 获取脚本所在目录作为默认路径
            script_dir = os.path.dirname(os.path.abspath(__file__))

            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    cache = json.load(f)
                    self.last_input_dir = cache.get('last_input_dir', script_dir)
                    self.last_output_dir = cache.get('last_output_dir', script_dir)
            else:
                self.last_input_dir = script_dir
                self.last_output_dir = script_dir
        except Exception as e:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            self.last_input_dir = script_dir
            self.last_output_dir = script_dir

    def save_cached_paths(self):
        """保存缓存的路径"""
        try:
            cache = {
                'last_input_dir': self.last_input_dir,
                'last_output_dir': self.last_output_dir
            }
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            pass

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="科陆流水线运维日志转换工具",
                               font=("微软雅黑", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 创建左右分栏
        # 左侧：文件选择和转换功能
        left_frame = ttk.LabelFrame(main_frame, text="文件转换", padding="15")
        left_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        left_frame.columnconfigure(1, weight=1)

        # 右侧：月视图
        right_frame = ttk.Frame(main_frame)
        right_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 输入文件选择
        ttk.Label(left_frame, text="选择CSV文件:", font=("微软雅黑", 10)).grid(
            row=0, column=0, sticky=tk.W, pady=(0, 10))

        input_entry = ttk.Entry(left_frame, textvariable=self.input_file,
                               font=("微软雅黑", 9), width=40)
        input_entry.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))

        input_button = ttk.Button(left_frame, text="浏览...",
                                 command=self.select_input_file)
        input_button.grid(row=1, column=2, padx=(10, 0), pady=(0, 5))
        
        # 输出文件选择
        ttk.Label(left_frame, text="选择输出位置:", font=("微软雅黑", 10)).grid(
            row=2, column=0, sticky=tk.W, pady=(20, 10))

        output_entry = ttk.Entry(left_frame, textvariable=self.output_file,
                                font=("微软雅黑", 9), width=40)
        output_entry.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))

        output_button = ttk.Button(left_frame, text="浏览...",
                                  command=self.select_output_file)
        output_button.grid(row=3, column=2, padx=(10, 0), pady=(0, 5))
        
        # 自动命名选项
        self.auto_name = tk.BooleanVar(value=True)
        auto_check = ttk.Checkbutton(left_frame, text="自动生成文件名 (科陆流水线运维日志YYYYMMDD.xlsx)",
                                    variable=self.auto_name, command=self.toggle_auto_name)
        auto_check.grid(row=4, column=0, columnspan=3, sticky=tk.W, pady=(10, 0))

        # 日期过滤选项
        self.use_date_filter = tk.BooleanVar(value=True)  # 默认勾选
        date_filter_check = ttk.Checkbutton(left_frame, text="仅输出选中日期的数据",
                                           variable=self.use_date_filter)
        date_filter_check.grid(row=5, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))

        # 转换按钮
        convert_button = ttk.Button(left_frame, text="开始转换",
                                   command=self.start_conversion,
                                   style="Accent.TButton")
        convert_button.grid(row=6, column=0, columnspan=3, pady=(30, 10))
        
        # 进度条
        self.progress = ttk.Progressbar(left_frame, mode='indeterminate')
        self.progress.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # 状态标签
        self.status_label = ttk.Label(left_frame, text="请选择CSV文件开始转换",
                                     font=("微软雅黑", 9))
        self.status_label.grid(row=8, column=0, columnspan=3, pady=(0, 10))

        # 日志文本框
        log_frame = ttk.LabelFrame(left_frame, text="转换日志", padding="10")
        log_frame.grid(row=9, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        left_frame.rowconfigure(9, weight=1)
        
        self.log_text = tk.Text(log_frame, height=8, font=("Consolas", 9))
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 创建月视图组件
        self.calendar_view = CalendarView(right_frame, on_date_selection_changed=self.on_date_selection_changed)
        self.calendar_view.pack(fill=tk.BOTH, expand=True)

        # 初始状态
        self.toggle_auto_name()

    def on_date_selection_changed(self, selected_dates):
        """日期选择变化回调"""
        self.selected_dates = selected_dates
        count = len(selected_dates)
        if count > 0:
            self.log(f"已选择 {count} 个日期用于数据过滤")
        else:
            self.log("已清除日期选择")

    def select_input_file(self):
        """选择输入CSV文件"""
        filename = filedialog.askopenfilename(
            title="选择CSV文件",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")],
            initialdir=self.last_input_dir
        )
        if filename:
            self.input_file.set(filename)
            self.log(f"已选择输入文件: {filename}")

            # 更新缓存路径
            self.last_input_dir = os.path.dirname(filename)
            self.save_cached_paths()

            # 加载日期数据到月视图
            if self.calendar_view:
                self.log("正在加载日期数据到月视图...")
                if self.calendar_view.load_dates_from_csv(filename):
                    self.log("月视图加载完成，可以选择需要输出的日期")
                else:
                    self.log("警告：无法从CSV文件中加载日期数据")

            # 如果启用自动命名，自动设置输出文件
            if self.auto_name.get():
                self.auto_set_output_file(filename)
    
    def select_output_file(self):
        """选择输出Excel文件"""
        if self.auto_name.get():
            # 如果启用自动命名，选择目录
            directory = filedialog.askdirectory(
                title="选择输出目录",
                initialdir=self.last_output_dir
            )
            if directory:
                timestamp = datetime.datetime.now().strftime('%Y%m%d')
                filename = os.path.join(directory, f"科陆流水线运维日志{timestamp}.xlsx")
                self.output_file.set(filename)
                self.log(f"已选择输出目录: {directory}")

                # 更新缓存路径
                self.last_output_dir = directory
                self.save_cached_paths()
        else:
            # 选择具体文件
            filename = filedialog.asksaveasfilename(
                title="保存Excel文件",
                defaultextension=".xlsx",
                filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")],
                initialdir=self.last_output_dir
            )
            if filename:
                self.output_file.set(filename)
                self.log(f"已选择输出文件: {filename}")

                # 更新缓存路径
                self.last_output_dir = os.path.dirname(filename)
                self.save_cached_paths()
    
    def auto_set_output_file(self, input_filename):
        """根据输入文件自动设置输出文件"""
        if self.auto_name.get():
            output_dir = os.path.dirname(input_filename)
            timestamp = datetime.datetime.now().strftime('%Y%m%d')
            output_filename = os.path.join(output_dir, f"科陆流水线运维日志{timestamp}.xlsx")
            self.output_file.set(output_filename)
            self.log(f"自动设置输出文件: {output_filename}")
    
    def toggle_auto_name(self):
        """切换自动命名模式"""
        if self.auto_name.get():
            # 如果有输入文件，自动设置输出文件
            if self.input_file.get():
                self.auto_set_output_file(self.input_file.get())
        
    def log(self, message):
        """添加日志消息"""
        timestamp = datetime.datetime.now().strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def update_status(self, message):
        """更新状态标签"""
        self.status_label.config(text=message)
        self.root.update_idletasks()

    def show_success_dialog(self, output_file):
        """显示成功对话框，包含打开目录和打开文件按钮"""
        dialog = tk.Toplevel(self.root)
        dialog.title("转换完成")
        dialog.geometry("400x200")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # 主框架
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 成功图标和消息
        success_label = ttk.Label(main_frame, text="✅ 转换完成！",
                                 font=("微软雅黑", 14, "bold"))
        success_label.pack(pady=(0, 10))

        # 文件路径
        file_label = ttk.Label(main_frame, text=f"输出文件：",
                              font=("微软雅黑", 10))
        file_label.pack(anchor=tk.W)

        path_label = ttk.Label(main_frame, text=output_file,
                              font=("微软雅黑", 9), foreground="blue")
        path_label.pack(anchor=tk.W, pady=(0, 20))

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        # 打开目录按钮
        def open_directory():
            try:
                os.startfile(os.path.dirname(output_file))
            except Exception as e:
                messagebox.showerror("错误", f"无法打开目录: {e}")
            dialog.destroy()

        # 打开文件按钮
        def open_file():
            try:
                os.startfile(output_file)
            except Exception as e:
                messagebox.showerror("错误", f"无法打开文件: {e}")
            dialog.destroy()

        # 关闭按钮
        def close_dialog():
            dialog.destroy()

        ttk.Button(button_frame, text="打开文件", command=open_file).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="打开目录", command=open_directory).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", command=close_dialog).pack(side=tk.RIGHT)
    
    def start_conversion(self):
        """开始转换（在新线程中）"""
        if not self.input_file.get():
            messagebox.showerror("错误", "请先选择输入CSV文件")
            return
        
        if not self.output_file.get():
            messagebox.showerror("错误", "请先选择输出位置")
            return
        
        # 在新线程中执行转换
        thread = threading.Thread(target=self.perform_conversion)
        thread.daemon = True
        thread.start()
    
    def perform_conversion(self):
        """执行转换"""
        try:
            # 开始进度条
            self.progress.start()
            self.update_status("正在转换...")

            self.log("开始转换...")
            self.log(f"输入文件: {self.input_file.get()}")
            self.log(f"输出文件: {self.output_file.get()}")

            # 检查输入文件是否存在
            if not os.path.exists(self.input_file.get()):
                raise FileNotFoundError(f"输入文件不存在: {self.input_file.get()}")

            # 检查输出目录是否存在
            output_dir = os.path.dirname(self.output_file.get())
            if output_dir and not os.path.exists(output_dir):
                self.log(f"创建输出目录: {output_dir}")
                os.makedirs(output_dir, exist_ok=True)

            # 重新导入更新后的转换器
            import importlib
            import csv_to_excel_converter
            importlib.reload(csv_to_excel_converter)
            from csv_to_excel_converter import CSVToExcelConverter

            # 创建自定义转换器实例，重定向输出到GUI
            class GUIConverter(CSVToExcelConverter):
                def __init__(self, gui_instance):
                    super().__init__()
                    self.gui = gui_instance

                def convert(self, csv_file_path, output_file_path=None):
                    try:
                        # 读取CSV文件
                        self.gui.log(f"正在读取CSV文件: {csv_file_path}")
                        df = self.read_csv_with_encoding(csv_file_path)

                        # 处理数据
                        self.gui.log("正在处理数据...")
                        df = self.clean_and_process_data(df)

                        if df.empty:
                            self.gui.log("警告: 处理后的数据为空")
                            return None

                        # 应用日期过滤
                        if self.gui.use_date_filter.get() and self.gui.selected_dates:
                            self.gui.log("正在应用日期过滤...")
                            original_count = len(df)

                            # 将选中的日期转换为字符串格式进行匹配
                            selected_date_strings = set()
                            for date_obj in self.gui.selected_dates:
                                # 生成多种日期格式，包括单数字和双数字格式
                                # 斜杠格式
                                selected_date_strings.add(date_obj.strftime('%Y/%m/%d'))      # 2025/07/11
                                selected_date_strings.add(f"{date_obj.year}/{date_obj.month}/{date_obj.day}")  # 2025/7/11

                                # 连字符格式
                                selected_date_strings.add(date_obj.strftime('%Y-%m-%d'))      # 2025-07-11
                                selected_date_strings.add(f"{date_obj.year}-{date_obj.month}-{date_obj.day}")  # 2025-7-11

                                # 点号格式
                                selected_date_strings.add(date_obj.strftime('%Y.%m.%d'))      # 2025.07.11
                                selected_date_strings.add(f"{date_obj.year}.{date_obj.month}.{date_obj.day}")  # 2025.7.11

                                # 尝试Unix格式（如果支持的话）
                                try:
                                    selected_date_strings.add(date_obj.strftime('%Y/%-m/%-d'))   # 2025/7/11 (Unix)
                                    selected_date_strings.add(date_obj.strftime('%Y-%-m-%-d'))   # 2025-7-11 (Unix)
                                    selected_date_strings.add(date_obj.strftime('%Y.%-m.%-d'))   # 2025.7.11 (Unix)
                                except ValueError:
                                    # Windows不支持%-格式，跳过
                                    pass

                            # 移除可能的重复项和None值
                            selected_date_strings = {s for s in selected_date_strings if s}

                            self.gui.log(f"生成的日期匹配格式: {sorted(selected_date_strings)}")

                            # 过滤数据
                            df = df[df['日期'].isin(selected_date_strings)]
                            filtered_count = len(df)

                            self.gui.log(f"日期过滤完成: {original_count} -> {filtered_count} 条记录")

                            if df.empty:
                                self.gui.log("警告: 日期过滤后数据为空，请检查选中的日期是否在CSV数据中存在")
                                return None

                        self.gui.log(f"最终处理了 {len(df)} 条记录")

                        # 生成输出文件路径
                        if output_file_path is None:
                            output_dir = os.path.dirname(csv_file_path)
                            timestamp = datetime.datetime.now().strftime('%Y%m%d')
                            output_file_path = os.path.join(output_dir, f"科陆流水线运维日志{timestamp}.xlsx")

                        # 创建Excel文件
                        self.gui.log("正在创建Excel文件...")
                        self.create_excel_with_formatting(df, output_file_path)

                        self.gui.log("转换完成!")
                        return output_file_path

                    except Exception as e:
                        self.gui.log(f"转换失败: {e}")
                        return None

            # 执行转换
            gui_converter = GUIConverter(self)
            result = gui_converter.convert(self.input_file.get(), self.output_file.get())

            # 停止进度条
            self.progress.stop()

            if result:
                self.log("转换完成!")
                self.update_status("转换完成")

                # 显示自定义成功对话框
                self.show_success_dialog(result)
            else:
                self.log("转换失败!")
                self.update_status("转换失败")
                messagebox.showerror("错误", "转换失败，请查看日志了解详情")

        except Exception as e:
            self.progress.stop()
            error_msg = f"转换过程中发生错误: {str(e)}"
            self.log(error_msg)
            self.update_status("转换失败")
            messagebox.showerror("错误", error_msg)


def main():
    """主函数"""
    root = tk.Tk()
    
    # 设置主题样式
    style = ttk.Style()
    try:
        style.theme_use('vista')  # Windows 10/11 样式
    except:
        try:
            style.theme_use('clam')  # 备用样式
        except:
            pass
    
    app = ConverterGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
