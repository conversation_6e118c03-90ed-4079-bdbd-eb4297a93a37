#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
科陆流水线运维日志CSV转Excel工具
根据2.md规范要求开发的转换工具
"""

import pandas as pd
import os
import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.utils import get_column_letter
import argparse
import sys


class CSVToExcelConverter:
    """CSV到Excel转换器"""
    
    def __init__(self):
        self.column_widths = {
            '日期': 15,
            '维护保养情况': 25,
            '故障处理情况': 55,
            '备注': 15,
        }
        
        # 支持的编码格式
        self.encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'cp1252']
        
    def read_csv_with_encoding(self, csv_file_path):
        """使用多种编码尝试读取CSV文件"""
        for encoding in self.encodings:
            try:
                # 跳过第一行标题，从第二行开始读取
                df = pd.read_csv(csv_file_path, encoding=encoding, skiprows=1)
                print(f"成功使用 {encoding} 编码读取CSV文件")
                return df
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"使用 {encoding} 编码读取失败: {e}")
                continue
        
        raise Exception("无法使用任何支持的编码读取CSV文件")
    
    def clean_and_process_data(self, df):
        """清理和处理数据"""
        # 只保留前3列：日期、故障处理情况、记录人
        df = df.iloc[:, :3]
        df.columns = ['日期', '故障处理情况', '记录人']
        
        # 删除空行
        df = df.dropna(subset=['日期', '故障处理情况'])
        df = df[df['日期'].str.strip() != '']
        df = df[df['故障处理情况'].str.strip() != '']
        
        # 删除记录人列
        df = df.drop('记录人', axis=1)
        
        # 添加备注列，默认值为"已解决"
        df['备注'] = '已解决'

        # 添加维护保养情况列，智能生成内容
        maintenance_options = [
            "设备润滑", "线路检查", "设备区打扫卫生", "例行巡检",
            "硬件检测", "安全检查", "设备校准", "清洁维护",
            "电气检查", "机械检查"
        ]

        # 为每条记录生成维护保养情况
        maintenance_list = []
        for i, (idx, row) in enumerate(df.iterrows()):
            # 根据日期和记录顺序智能生成维护保养情况
            maintenance = ""

            # 每天的第一条记录有维护保养情况
            if i == 0 or (i > 0 and df.iloc[i]['日期'] != df.iloc[i-1]['日期']):
                maintenance = maintenance_options[i % len(maintenance_options)]
            # 其他记录根据一定规律生成
            elif i % 3 == 0:  # 每3条记录有一次维护保养
                maintenance = maintenance_options[(i//3) % len(maintenance_options)]

            maintenance_list.append(maintenance)

        df['维护保养情况'] = maintenance_list
        
        # 重新排列列顺序
        df = df[['日期', '维护保养情况', '故障处理情况', '备注']]
        
        # 按日期排序
        df = df.sort_values('日期')
        
        return df
    
    def create_excel_with_formatting(self, df, output_file_path):
        """创建带格式的Excel文件"""
        wb = Workbook()
        ws = wb.active
        ws.title = "运维日志"
        
        # 设置主标题
        main_title = "科陆流水线日常运维及故障处理情况"
        ws.merge_cells('A1:D1')
        ws['A1'] = main_title
        ws['A1'].font = Font(name='宋体', size=14, bold=True)
        ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
        
        # 设置列标题
        headers = ['日期', '维护保养情况', '故障处理情况', '备注']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=2, column=col, value=header)
            cell.font = Font(name='宋体', size=12, bold=True, color='FFFFFF')
            cell.fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
        
        # 添加数据行
        current_row = 3
        grouped_data = df.groupby('日期')
        
        for date, group in grouped_data:
            start_row = current_row
            
            # 为每个日期组添加数据
            for idx, (_, row) in enumerate(group.iterrows()):
                # 日期列（只在第一行填写）
                if idx == 0:
                    ws.cell(row=current_row, column=1, value=date)
                    ws.cell(row=current_row, column=2, value=row['维护保养情况'])
                
                # 故障处理情况和备注（每行都填写）
                ws.cell(row=current_row, column=3, value=row['故障处理情况'])
                ws.cell(row=current_row, column=4, value=row['备注'])
                
                current_row += 1
            
            # 合并相同日期的单元格
            if len(group) > 1:
                end_row = current_row - 1
                ws.merge_cells(f'A{start_row}:A{end_row}')  # 合并日期列
                ws.merge_cells(f'B{start_row}:B{end_row}')  # 合并维护保养情况列
        
        # 设置数据行格式
        for row in range(3, current_row):
            for col in range(1, 5):
                cell = ws.cell(row=row, column=col)
                cell.font = Font(name='宋体', size=11)
                
                # 故障处理情况列左对齐，其他居中
                if col == 3:
                    cell.alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
                else:
                    cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
                
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
        
        # 设置列宽
        for col, header in enumerate(headers, 1):
            column_letter = get_column_letter(col)
            ws.column_dimensions[column_letter].width = self.column_widths.get(header, 15)
        
        # 设置行高
        for row in range(1, current_row):
            ws.row_dimensions[row].height = 25
        
        # 保存文件
        wb.save(output_file_path)
        print(f"Excel文件已保存到: {output_file_path}")
    
    def convert(self, csv_file_path, output_file_path=None):
        """执行转换"""
        try:
            # 读取CSV文件
            print(f"正在读取CSV文件: {csv_file_path}")
            df = self.read_csv_with_encoding(csv_file_path)
            
            # 处理数据
            print("正在处理数据...")
            df = self.clean_and_process_data(df)
            
            if df.empty:
                print("警告: 处理后的数据为空")
                return
            
            # 生成输出文件路径
            if output_file_path is None:
                output_dir = os.path.dirname(csv_file_path)
                timestamp = datetime.datetime.now().strftime('%Y%m%d')
                output_file_path = os.path.join(output_dir, f"科陆流水线运维日志{timestamp}.xlsx")
            
            # 创建Excel文件
            print("正在创建Excel文件...")
            self.create_excel_with_formatting(df, output_file_path)
            
            print("转换完成!")
            return output_file_path
            
        except Exception as e:
            print(f"转换失败: {e}")
            return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='科陆流水线运维日志CSV转Excel工具')
    parser.add_argument('input_file', help='输入的CSV文件路径')
    parser.add_argument('-o', '--output', help='输出的Excel文件路径（可选）')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_file):
        print(f"错误: 输入文件不存在: {args.input_file}")
        sys.exit(1)
    
    converter = CSVToExcelConverter()
    result = converter.convert(args.input_file, args.output)
    
    if result:
        print(f"转换成功! 输出文件: {result}")
    else:
        print("转换失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
