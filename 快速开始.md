# 月视图功能快速开始指南

## 🚀 立即体验

### 方法1: 启动完整GUI工具
```bash
# 双击运行
启动GUI工具.bat

# 或命令行运行
python gui_converter.py
```

### 方法2: 体验月视图演示
```bash
# 双击运行
启动月视图演示.bat

# 或命令行运行
python 演示月视图功能.py
```

## 📋 快速操作步骤

### 1. 启动工具
- 双击 `启动GUI工具.bat`
- 界面分为左右两部分：左侧文件操作，右侧月视图

### 2. 选择文件
- 点击左侧"浏览..."按钮
- 选择 `日报客户-工作表1.csv` 文件
- 右侧月视图自动显示可用日期（蓝色背景）

### 3. 选择日期
- 在右侧月视图中点击需要的日期
- 选中的日期变为绿色背景
- 可以选择多个日期

### 4. 配置输出
- 勾选"仅输出选中日期的数据"启用过滤
- 配置输出位置（或使用自动命名）

### 5. 执行转换
- 点击"开始转换"
- 查看转换日志了解过滤结果
- 转换完成后可直接打开文件

## 🎯 使用示例

### 示例1: 输出单日数据
1. 选择CSV文件
2. 点击7月11日（显示为蓝色）
3. 勾选日期过滤选项
4. 转换 → 输出4条记录

### 示例2: 输出多日数据
1. 选择CSV文件
2. 点击7月22、23、24日
3. 勾选日期过滤选项
4. 转换 → 输出15条记录

### 示例3: 输出全部数据
1. 选择CSV文件
2. 不勾选日期过滤选项
3. 转换 → 输出全部40条记录

## 🔧 月视图操作

### 日期状态说明
- **蓝色背景**: 可用日期（CSV中有数据）
- **绿色背景**: 已选中的日期
- **灰色文字**: 不可用日期

### 快捷操作
- **◀ ▶**: 切换月份
- **全选本月**: 选择当前月所有可用日期
- **清除选择**: 清除所有选择
- **状态显示**: 显示已选择日期数量

## 📊 数据概览

当前演示数据包含：
- **总记录数**: 40条
- **日期范围**: 2025年7月
- **可用日期**: 7个（11, 16, 22, 23, 24, 25, 26日）

各日期记录数：
- 7月11日: 4条记录
- 7月16日: 4条记录
- 7月22日: 5条记录
- 7月23日: 5条记录
- 7月24日: 5条记录
- 7月25日: 5条记录
- 7月26日: 12条记录

## ⚡ 快速测试

想要快速测试功能？运行：
```bash
python 测试月视图转换.py
```

这会自动：
1. 读取演示CSV文件
2. 过滤出7月11日的数据
3. 生成测试Excel文件
4. 显示过滤结果

## 🆘 常见问题

**Q: 月视图没有显示日期？**
A: 确保CSV文件第一列包含有效的日期数据

**Q: 点击日期没有反应？**
A: 只有蓝色背景的日期可以点击选择

**Q: 转换后数据没有被过滤？**
A: 确保勾选了"仅输出选中日期的数据"选项

**Q: 如何选择多个日期？**
A: 依次点击需要的日期，或使用"全选本月"

## 📚 更多信息

- 详细说明: `月视图功能说明.md`
- 完整文档: `README.md`
- 实现总结: `月视图功能实现总结.md`
