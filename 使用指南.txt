科陆流水线运维日志转换工具 - 使用指南
===========================================

📋 工具简介
-----------
这是一个专门为科陆流水线运维日志设计的CSV到Excel转换工具，
能够将CSV格式的运维记录转换为格式化的Excel文件。

🚀 快速开始
-----------
1. 确保已安装Python 3.7或更高版本
2. 双击"启动GUI工具.bat"启动图形界面
3. 选择CSV文件和输出位置
4. 点击"开始转换"

📁 文件说明
-----------
- 启动GUI工具.bat     - 双击启动GUI工具（推荐）
- gui_converter.py     - GUI版本转换工具
- csv_to_excel_converter.py - 命令行版本转换工具
- run_converter.py     - 简单运行脚本
- README.md           - 详细说明文档

🎯 主要功能
-----------
✅ 支持多种编码格式的CSV文件
✅ 自动添加"备注"和"维护保养情况"列
✅ 智能生成维护保养情况内容（设备润滑、线路检查、例行巡检等）
✅ 按日期分组并合并相同日期的单元格
✅ 专业的Excel格式化（字体、颜色、边框等）
✅ 自动生成规范的文件名
✅ 图形界面操作简单直观
✅ 智能路径缓存，记住上次使用的目录
✅ 转换完成后可直接打开文件或目录

🔧 维护保养情况生成规则
-----------------------
- 每天的第一条记录自动添加维护保养情况
- 每3条记录中有一条包含维护保养情况
- 维护保养类型包括：设备润滑、线路检查、设备区打扫卫生、例行巡检、
  硬件检测、安全检查、设备校准、清洁维护、电气检查、机械检查、
  软件更新、备件检查等

📊 输出格式
-----------
- 主标题：科陆流水线日常运维及故障处理情况
- 列结构：日期 | 维护保养情况 | 故障处理情况 | 备注
- 文件名：科陆流水线运维日志YYYYMMDD.xlsx

🔧 使用方法
-----------
方法1 - GUI界面（推荐）：
1. 双击"启动GUI工具.bat"
2. 选择CSV文件（自动记住上次使用的目录）
3. 选择输出位置（自动记住上次使用的目录）
4. 点击"开始转换"
5. 转换完成后可选择"打开目录"或"打开文件"

方法2 - 命令行：
python csv_to_excel_converter.py "输入文件.csv"

方法3 - 简单脚本：
python run_converter.py

❓ 常见问题
-----------
Q: 提示缺少依赖包怎么办？
A: 运行：pip install pandas openpyxl -i https://pypi.tuna.tsinghua.edu.cn/simple

Q: CSV文件编码有问题怎么办？
A: 工具支持多种编码自动检测，包括UTF-8、GBK、GB2312等

Q: 输出文件在哪里？
A: 默认保存在输入文件相同目录，文件名为"科陆流水线运维日志YYYYMMDD.xlsx"

Q: 可以自定义输出文件名吗？
A: 可以，在GUI中取消"自动生成文件名"选项，或使用命令行的-o参数

📞 技术支持
-----------
如遇到问题，请检查：
1. Python版本是否为3.7+
2. 依赖包是否已安装
3. CSV文件格式是否正确
4. 输出目录是否有写入权限

更多详细信息请查看README.md文件。
